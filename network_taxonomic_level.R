# Network Analysis at Taxonomic Level (Genus or Family)
# This script aggregates OTUs to higher taxonomic levels before network analysis
# to reduce computational load while preserving biological meaning

# Load required libraries
library(readxl)
library(igraph)
library(vegan)
library(tidyverse)

# Configuration: Choose taxonomic level for analysis
TAXONOMIC_LEVEL <- "Family"  # Options: "Genus", "Family", "Order", "Class"
cat("Performing network analysis at", TAXONOMIC_LEVEL, "level\n")
cat("This provides optimal balance between computational efficiency and biological resolution\n")


# Read data files for the new experiment with four datasets
cat("========== LOADING DATA FILES ==========\n")

# Bacteria datasets
bacteria_alpine_otu <- read_excel("OTU_table_bacteria_alpine_samples.xlsx")
bacteria_farming_otu <- read_excel("OTU_table_bacteria_farming_samples.xlsx")

# Fungi datasets  
fungi_alpine_otu <- read_excel("OTU_table_fungi_alpine_samples.xlsx")
fungi_farming_otu <- read_excel("OTU_table_fungi_farming_samples.xlsx")

# Taxonomy files
bacteria_alpine_tax <- read_excel("taxonomy_bacteria_alpine_samples.xlsx")
bacteria_farming_tax <- read_excel("taxonomy_bacteria_farming_samples.xlsx")
fungi_alpine_tax <- read_excel("taxonomy_fungi_alpine_samples.xlsx")
fungi_farming_tax <- read_excel("taxonomy_fungi_farming_samples.xlsx")

# Metadata files
bacteria_alpine_meta <- read_excel("metadata_bacteria_alpine_samples.xlsx")
bacteria_farming_meta <- read_excel("metadata_bacteria_farming_samples.xlsx")
fungi_alpine_meta <- read_excel("metadata_fungi_alpine_samples.xlsx")
fungi_farming_meta <- read_excel("metadata_fungi_farming_samples.xlsx")

# Print data dimensions for debugging
cat("Original data dimensions:\n")
cat("Bacteria Alpine OTU:", dim(bacteria_alpine_otu), "\n")
cat("Bacteria Farming OTU:", dim(bacteria_farming_otu), "\n")
cat("Fungi Alpine OTU:", dim(fungi_alpine_otu), "\n")
cat("Fungi Farming OTU:", dim(fungi_farming_otu), "\n")

# Function to aggregate OTUs to specified taxonomic level
aggregate_to_taxonomic_level <- function(otu_table, taxonomy_table, taxonomic_level) {
  cat("\n--- Aggregating to", taxonomic_level, "level ---\n")
  
  # Check if taxonomic level exists in taxonomy table
  if (!taxonomic_level %in% colnames(taxonomy_table)) {
    stop("Taxonomic level '", taxonomic_level, "' not found in taxonomy table")
  }
  
  # Get the first column name (OTU ID column)
  otu_id_col <- colnames(otu_table)[1]
  tax_otu_id_col <- colnames(taxonomy_table)[1]
  
  # Merge OTU table with taxonomy
  merged_data <- merge(otu_table, taxonomy_table[, c(tax_otu_id_col, taxonomic_level)], 
                       by.x = otu_id_col, by.y = tax_otu_id_col, all.x = TRUE)
  
  # Remove rows with NA or empty taxonomic assignment
  valid_rows <- !is.na(merged_data[[taxonomic_level]]) & 
                merged_data[[taxonomic_level]] != "" & 
                merged_data[[taxonomic_level]] != "NA"
  
  if (sum(valid_rows) == 0) {
    stop("No valid taxonomic assignments found at ", taxonomic_level, " level")
  }
  
  cat("Removed", sum(!valid_rows), "OTUs with missing", taxonomic_level, "assignment\n")
  merged_data <- merged_data[valid_rows, ]
  
  # Get sample columns (all columns except OTU ID and taxonomic level)
  sample_cols <- setdiff(colnames(merged_data), c(otu_id_col, taxonomic_level))
  
  # Aggregate by summing OTU counts within each taxonomic group
  aggregated <- merged_data %>%
    group_by(!!sym(taxonomic_level)) %>%
    summarise(across(all_of(sample_cols), sum, na.rm = TRUE), .groups = 'drop')
  
  # Rename the taxonomic level column to match original OTU ID column name
  colnames(aggregated)[1] <- otu_id_col
  
  cat("Aggregated from", nrow(otu_table), "OTUs to", nrow(aggregated), taxonomic_level, "groups\n")
  
  return(as.data.frame(aggregated))
}

# Function to filter samples by environment (old vs new)
filter_samples_by_environment <- function(otu_table, metadata, environment_type) {
  # Determine the sample ID column name (different files use different names)
  sample_id_col <- NULL
  if ("Sample_ID" %in% colnames(metadata)) {
    sample_id_col <- "Sample_ID"
  } else if ("sample" %in% colnames(metadata)) {
    sample_id_col <- "sample"
  } else {
    cat("Error: Could not find sample ID column in metadata\n")
    return(NULL)
  }
  
  # Get sample IDs for the specified environment
  target_samples <- metadata[[sample_id_col]][grepl(environment_type, metadata$Environment, ignore.case = TRUE)]
  
  cat("Found", length(target_samples), "target samples for environment", environment_type, "\n")
  
  # Find which columns in OTU table correspond to these samples
  sample_cols <- which(colnames(otu_table) %in% target_samples)
  
  if (length(sample_cols) == 0) {
    cat("Warning: No samples found for environment", environment_type, "\n")
    return(NULL)
  }
  
  # Return OTU table with only the target samples (plus the first column with IDs)
  filtered_table <- otu_table[, c(1, sample_cols)]
  cat("Filtered", environment_type, "samples:", ncol(filtered_table) - 1, "samples\n")
  return(filtered_table)
}

# Aggregate all datasets to the specified taxonomic level
cat("\n========== AGGREGATING TO TAXONOMIC LEVEL ==========\n")

bacteria_alpine_agg <- aggregate_to_taxonomic_level(bacteria_alpine_otu, bacteria_alpine_tax, TAXONOMIC_LEVEL)
bacteria_farming_agg <- aggregate_to_taxonomic_level(bacteria_farming_otu, bacteria_farming_tax, TAXONOMIC_LEVEL)
fungi_alpine_agg <- aggregate_to_taxonomic_level(fungi_alpine_otu, fungi_alpine_tax, TAXONOMIC_LEVEL)
fungi_farming_agg <- aggregate_to_taxonomic_level(fungi_farming_otu, fungi_farming_tax, TAXONOMIC_LEVEL)

cat("\nAggregated data dimensions:\n")
cat("Bacteria Alpine:", dim(bacteria_alpine_agg), "\n")
cat("Bacteria Farming:", dim(bacteria_farming_agg), "\n")
cat("Fungi Alpine:", dim(fungi_alpine_agg), "\n")
cat("Fungi Farming:", dim(fungi_farming_agg), "\n")

# Create filtered datasets for old and new samples
cat("\n========== FILTERING SAMPLES BY ENVIRONMENT ==========\n")

# Bacteria Alpine
bacteria_alpine_old <- filter_samples_by_environment(bacteria_alpine_agg, bacteria_alpine_meta, "old")
bacteria_alpine_new <- filter_samples_by_environment(bacteria_alpine_agg, bacteria_alpine_meta, "new")

# Bacteria Farming  
bacteria_farming_old <- filter_samples_by_environment(bacteria_farming_agg, bacteria_farming_meta, "old")
bacteria_farming_new <- filter_samples_by_environment(bacteria_farming_agg, bacteria_farming_meta, "new")

# Fungi Alpine
fungi_alpine_old <- filter_samples_by_environment(fungi_alpine_agg, fungi_alpine_meta, "old")
fungi_alpine_new <- filter_samples_by_environment(fungi_alpine_agg, fungi_alpine_meta, "new")

# Fungi Farming
fungi_farming_old <- filter_samples_by_environment(fungi_farming_agg, fungi_farming_meta, "old")
fungi_farming_new <- filter_samples_by_environment(fungi_farming_agg, fungi_farming_meta, "new")

cat("\n========== SUMMARY OF FILTERED DATASETS ==========\n")
if (!is.null(bacteria_alpine_old)) cat("Bacteria Alpine Old:", dim(bacteria_alpine_old), "\n")
if (!is.null(bacteria_alpine_new)) cat("Bacteria Alpine New:", dim(bacteria_alpine_new), "\n")
if (!is.null(bacteria_farming_old)) cat("Bacteria Farming Old:", dim(bacteria_farming_old), "\n")
if (!is.null(bacteria_farming_new)) cat("Bacteria Farming New:", dim(bacteria_farming_new), "\n")
if (!is.null(fungi_alpine_old)) cat("Fungi Alpine Old:", dim(fungi_alpine_old), "\n")
if (!is.null(fungi_alpine_new)) cat("Fungi Alpine New:", dim(fungi_alpine_new), "\n")
if (!is.null(fungi_farming_old)) cat("Fungi Farming Old:", dim(fungi_farming_old), "\n")
if (!is.null(fungi_farming_new)) cat("Fungi Farming New:", dim(fungi_farming_new), "\n")

cat("\nData aggregation and filtering completed successfully!\n")

# Function to create correlation matrix for taxonomic-level data
create_taxonomic_correlation_matrix <- function(taxonomic_table, threshold = 0.6) {
  cat("\n--- Creating correlation matrix ---\n")
  cat("Input dimensions:", dim(taxonomic_table), "\n")

  # Check if the table has at least one column besides the taxonomic IDs
  if (ncol(taxonomic_table) <= 1) {
    stop("Table must have at least one sample column besides the taxonomic IDs")
  }

  tryCatch({
    # Extract abundance matrix (remove first column which contains taxonomic names)
    abundance_matrix <- as.matrix(taxonomic_table[,-1])
    rownames(abundance_matrix) <- taxonomic_table[[1]]

    # Check for rows with all zeros or constant values
    row_sums <- rowSums(abundance_matrix)
    constant_rows <- row_sums == 0 | apply(abundance_matrix, 1, function(x) length(unique(x)) <= 1)

    if (any(constant_rows)) {
      cat("Removing", sum(constant_rows), "constant rows (all zeros or all same value)\n")
      abundance_matrix <- abundance_matrix[!constant_rows, , drop = FALSE]
    }

    # Check if we still have enough rows
    if (nrow(abundance_matrix) <= 1) {
      stop("Not enough variable rows for correlation analysis after removing constant rows")
    }

    cat("Final matrix for correlation:", nrow(abundance_matrix), "taxa ×", ncol(abundance_matrix), "samples\n")

    # Calculate correlations
    cat("Calculating Spearman correlations...\n")
    cor_matrix <- cor(t(abundance_matrix), method = "spearman", use = "pairwise.complete.obs")

    # Replace any NAs with 0
    if (any(is.na(cor_matrix))) {
      cat("Replacing", sum(is.na(cor_matrix)), "NA values with 0\n")
      cor_matrix[is.na(cor_matrix)] <- 0
    }

    # Print summary before thresholding
    cat("\nBefore thresholding:\n")
    cat("Total correlations:", length(cor_matrix), "\n")
    cat("Positive correlations:", sum(cor_matrix > 0), "\n")
    cat("Negative correlations:", sum(cor_matrix < 0), "\n")
    cat("Range:", range(cor_matrix), "\n")

    # Store signs before thresholding
    signs <- sign(cor_matrix)

    # Apply threshold to absolute values
    cor_matrix[abs(cor_matrix) < threshold] <- 0

    # Restore signs
    cor_matrix <- cor_matrix * signs

    # Set diagonal to zero
    diag(cor_matrix) <- 0

    # Print summary after thresholding
    cat("\nAfter thresholding (|correlation| ≥", threshold, "):\n")
    cat("Total non-zero correlations:", sum(cor_matrix != 0), "\n")
    cat("Positive correlations:", sum(cor_matrix > 0), "\n")
    cat("Negative correlations:", sum(cor_matrix < 0), "\n")

    if (sum(cor_matrix != 0) > 0) {
      cat("Range of non-zero correlations:", range(cor_matrix[cor_matrix != 0]), "\n")
    } else {
      cat("No correlations above threshold.\n")
    }

    return(list(
      cor_matrix = cor_matrix,
      taxa_names = rownames(abundance_matrix),
      n_taxa = nrow(abundance_matrix)
    ))

  }, error = function(e) {
    cat("Error in correlation calculation:", e$message, "\n")
    return(NULL)
  })
}

# Function to analyze taxonomic network
analyze_taxonomic_network <- function(cor_result, group_name) {
  if (is.null(cor_result)) {
    cat("Cannot analyze network: correlation result is NULL\n")
    return(NULL)
  }

  cat("\n========== ANALYZING", group_name, "NETWORK ==========\n")

  cor_matrix <- cor_result$cor_matrix
  taxa_names <- cor_result$taxa_names

  # Check if we have any correlations
  if (sum(cor_matrix != 0) == 0) {
    cat("No correlations above threshold. Cannot create network.\n")
    return(NULL)
  }

  tryCatch({
    # Create igraph network from correlation matrix
    network <- graph_from_adjacency_matrix(
      abs(cor_matrix),
      mode = "undirected",
      weighted = TRUE,
      diag = FALSE
    )

    # Add taxa names as vertex names
    V(network)$name <- taxa_names

    # Get edge list to properly assign edge attributes
    edge_list <- as_edgelist(network, names = FALSE)

    # Extract correlation values for each edge
    edge_correlations <- numeric(nrow(edge_list))
    edge_signs <- character(nrow(edge_list))

    for (i in 1:nrow(edge_list)) {
      row_idx <- edge_list[i, 1]
      col_idx <- edge_list[i, 2]
      correlation_value <- cor_matrix[row_idx, col_idx]
      edge_correlations[i] <- correlation_value
      edge_signs[i] <- ifelse(correlation_value > 0, "positive", "negative")
    }

    # Add edge attributes
    E(network)$sign <- edge_signs
    E(network)$correlation <- edge_correlations

    # Calculate network metrics
    cat("Network created with", vcount(network), "nodes and", ecount(network), "edges\n")

    # Node degree
    node_degrees <- degree(network)
    cat("Average degree:", mean(node_degrees), "\n")
    cat("Max degree:", max(node_degrees), "\n")

    # Centrality measures
    betweenness <- betweenness(network)
    closeness <- closeness(network)

    # Community detection
    communities <- cluster_louvain(network)
    community_count <- length(communities)
    modularity_score <- modularity(communities)

    cat("Communities detected:", community_count, "\n")
    cat("Modularity:", modularity_score, "\n")

    # Identify hub nodes (top 5% by degree for more selectivity)
    degree_threshold <- quantile(node_degrees, 0.95)
    hub_nodes <- names(node_degrees[node_degrees >= degree_threshold])
    cat("Hub nodes (top 5% by degree):", length(hub_nodes), "\n")

    # Create results list
    results <- list(
      network = network,
      node_degrees = node_degrees,
      betweenness = betweenness,
      closeness = closeness,
      communities = communities,
      community_count = community_count,
      modularity = modularity_score,
      hub_nodes = hub_nodes,
      taxa_names = taxa_names,
      group_name = group_name
    )

    return(results)

  }, error = function(e) {
    cat("Error in network analysis:", e$message, "\n")
    return(NULL)
  })
}

# Perform individual network analyses for each dataset
cat("\n\n========== INDIVIDUAL NETWORK ANALYSES ==========\n")

# Bacteria Alpine Old
if (!is.null(bacteria_alpine_old)) {
  bacteria_alpine_old_cor <- create_taxonomic_correlation_matrix(bacteria_alpine_old, threshold = 0.6)
  bacteria_alpine_old_network <- analyze_taxonomic_network(bacteria_alpine_old_cor, "Bacteria_Alpine_Old")
}

# Bacteria Alpine New
if (!is.null(bacteria_alpine_new)) {
  bacteria_alpine_new_cor <- create_taxonomic_correlation_matrix(bacteria_alpine_new, threshold = 0.6)
  bacteria_alpine_new_network <- analyze_taxonomic_network(bacteria_alpine_new_cor, "Bacteria_Alpine_New")
}

# Bacteria Farming Old
if (!is.null(bacteria_farming_old)) {
  bacteria_farming_old_cor <- create_taxonomic_correlation_matrix(bacteria_farming_old, threshold = 0.6)
  bacteria_farming_old_network <- analyze_taxonomic_network(bacteria_farming_old_cor, "Bacteria_Farming_Old")
}

# Bacteria Farming New
if (!is.null(bacteria_farming_new)) {
  bacteria_farming_new_cor <- create_taxonomic_correlation_matrix(bacteria_farming_new, threshold = 0.6)
  bacteria_farming_new_network <- analyze_taxonomic_network(bacteria_farming_new_cor, "Bacteria_Farming_New")
}

# Fungi Alpine Old
if (!is.null(fungi_alpine_old)) {
  fungi_alpine_old_cor <- create_taxonomic_correlation_matrix(fungi_alpine_old, threshold = 0.6)
  fungi_alpine_old_network <- analyze_taxonomic_network(fungi_alpine_old_cor, "Fungi_Alpine_Old")
}

# Fungi Alpine New
if (!is.null(fungi_alpine_new)) {
  fungi_alpine_new_cor <- create_taxonomic_correlation_matrix(fungi_alpine_new, threshold = 0.6)
  fungi_alpine_new_network <- analyze_taxonomic_network(fungi_alpine_new_cor, "Fungi_Alpine_New")
}

# Fungi Farming Old
if (!is.null(fungi_farming_old)) {
  fungi_farming_old_cor <- create_taxonomic_correlation_matrix(fungi_farming_old, threshold = 0.6)
  fungi_farming_old_network <- analyze_taxonomic_network(fungi_farming_old_cor, "Fungi_Farming_Old")
}

# Fungi Farming New
if (!is.null(fungi_farming_new)) {
  fungi_farming_new_cor <- create_taxonomic_correlation_matrix(fungi_farming_new, threshold = 0.6)
  fungi_farming_new_network <- analyze_taxonomic_network(fungi_farming_new_cor, "Fungi_Farming_New")
}

# Function to create cross-correlation matrix between two taxonomic tables
create_taxonomic_cross_correlation_matrix <- function(table1, table2, threshold = 0.6) {
  cat("\n--- Creating cross-correlation matrix ---\n")
  cat("Table 1 dimensions:", dim(table1), "\n")
  cat("Table 2 dimensions:", dim(table2), "\n")

  tryCatch({
    # Extract abundance matrices (remove first column which contains taxonomic names)
    abundance_matrix1 <- as.matrix(table1[,-1])
    abundance_matrix2 <- as.matrix(table2[,-1])

    rownames(abundance_matrix1) <- table1[[1]]
    rownames(abundance_matrix2) <- table2[[1]]

    # Remove constant rows from both matrices
    row_sums1 <- rowSums(abundance_matrix1)
    constant_rows1 <- row_sums1 == 0 | apply(abundance_matrix1, 1, function(x) length(unique(x)) <= 1)
    if (any(constant_rows1)) {
      cat("Removing", sum(constant_rows1), "constant rows from table 1\n")
      abundance_matrix1 <- abundance_matrix1[!constant_rows1, , drop = FALSE]
    }

    row_sums2 <- rowSums(abundance_matrix2)
    constant_rows2 <- row_sums2 == 0 | apply(abundance_matrix2, 1, function(x) length(unique(x)) <= 1)
    if (any(constant_rows2)) {
      cat("Removing", sum(constant_rows2), "constant rows from table 2\n")
      abundance_matrix2 <- abundance_matrix2[!constant_rows2, , drop = FALSE]
    }

    # Check if we have enough data
    if (nrow(abundance_matrix1) <= 1 || nrow(abundance_matrix2) <= 1) {
      stop("Not enough variable rows for cross-correlation analysis")
    }

    # Get sample names and find common samples
    samples1 <- colnames(abundance_matrix1)
    samples2 <- colnames(abundance_matrix2)
    common_samples <- intersect(samples1, samples2)

    if (length(common_samples) == 0) {
      stop("No common samples found between the two tables")
    }

    cat("Found", length(common_samples), "common samples\n")

    # Subset to common samples
    abundance_matrix1 <- abundance_matrix1[, common_samples, drop = FALSE]
    abundance_matrix2 <- abundance_matrix2[, common_samples, drop = FALSE]

    cat("Final matrices: ", nrow(abundance_matrix1), "×", ncol(abundance_matrix1),
        " vs ", nrow(abundance_matrix2), "×", ncol(abundance_matrix2), "\n")

    # Calculate cross-correlations
    cat("Calculating cross-correlations...\n")
    cross_cor_matrix <- cor(t(abundance_matrix1), t(abundance_matrix2),
                           method = "spearman", use = "pairwise.complete.obs")

    # Replace any NAs with 0
    if (any(is.na(cross_cor_matrix))) {
      cat("Replacing", sum(is.na(cross_cor_matrix)), "NA values with 0\n")
      cross_cor_matrix[is.na(cross_cor_matrix)] <- 0
    }

    # Print summary before thresholding
    cat("\nBefore thresholding:\n")
    cat("Total correlations:", length(cross_cor_matrix), "\n")
    cat("Positive correlations:", sum(cross_cor_matrix > 0), "\n")
    cat("Negative correlations:", sum(cross_cor_matrix < 0), "\n")
    cat("Range:", range(cross_cor_matrix), "\n")

    # Apply threshold
    cross_cor_matrix[abs(cross_cor_matrix) < threshold] <- 0

    # Print summary after thresholding
    cat("\nAfter thresholding (|correlation| ≥", threshold, "):\n")
    cat("Total non-zero correlations:", sum(cross_cor_matrix != 0), "\n")
    cat("Positive correlations:", sum(cross_cor_matrix > 0), "\n")
    cat("Negative correlations:", sum(cross_cor_matrix < 0), "\n")

    if (sum(cross_cor_matrix != 0) > 0) {
      cat("Range of non-zero correlations:", range(cross_cor_matrix[cross_cor_matrix != 0]), "\n")
    } else {
      cat("No correlations above threshold.\n")
    }

    return(list(
      cor_matrix = cross_cor_matrix,
      taxa_names1 = rownames(abundance_matrix1),
      taxa_names2 = rownames(abundance_matrix2),
      common_samples = common_samples
    ))

  }, error = function(e) {
    cat("Error in cross-correlation calculation:", e$message, "\n")
    return(NULL)
  })
}

# Function to analyze cross-correlation network
analyze_taxonomic_cross_network <- function(cross_cor_result, group1_name, group2_name) {
  if (is.null(cross_cor_result)) {
    cat("Cannot analyze cross-network: correlation result is NULL\n")
    return(NULL)
  }

  cat("\n========== ANALYZING", group1_name, "vs", group2_name, "CROSS-NETWORK ==========\n")

  cross_cor_matrix <- cross_cor_result$cor_matrix
  taxa_names1 <- cross_cor_result$taxa_names1
  taxa_names2 <- cross_cor_result$taxa_names2

  # Check if we have any correlations
  if (sum(cross_cor_matrix != 0) == 0) {
    cat("No correlations above threshold. Cannot create cross-network.\n")
    return(NULL)
  }

  tryCatch({
    # Create bipartite network
    # First, create an edge list from the correlation matrix
    edges <- which(cross_cor_matrix != 0, arr.ind = TRUE)
    edge_list <- data.frame(
      from = taxa_names1[edges[,1]],
      to = taxa_names2[edges[,2]],
      weight = abs(cross_cor_matrix[edges]),
      correlation = cross_cor_matrix[edges],
      sign = ifelse(cross_cor_matrix[edges] > 0, "positive", "negative"),
      stringsAsFactors = FALSE
    )

    # Create igraph network
    network <- graph_from_data_frame(edge_list, directed = FALSE)

    # Debug information
    cat("Created network with", vcount(network), "vertices and", ecount(network), "edges\n")
    cat("Vertex names sample:", head(V(network)$name, 10), "\n")

    # Add vertex attributes to distinguish between the two groups
    V(network)$type <- ifelse(V(network)$name %in% taxa_names1, group1_name, group2_name)

    # Verify vertex types
    cat("Group assignments - ", group1_name, ":", sum(V(network)$type == group1_name),
        ", ", group2_name, ":", sum(V(network)$type == group2_name), "\n")

    # Calculate network metrics
    cat("Cross-network created with", vcount(network), "nodes and", ecount(network), "edges\n")
    cat("Group 1 (", group1_name, ") nodes:", sum(V(network)$type == group1_name), "\n")
    cat("Group 2 (", group2_name, ") nodes:", sum(V(network)$type == group2_name), "\n")

    # Node degree
    node_degrees <- degree(network)
    cat("Average degree:", mean(node_degrees), "\n")
    cat("Max degree:", max(node_degrees), "\n")

    # Centrality measures
    betweenness <- betweenness(network)
    closeness <- closeness(network)

    # Community detection
    communities <- cluster_louvain(network)
    community_count <- length(communities)
    modularity_score <- modularity(communities)

    cat("Communities detected:", community_count, "\n")
    cat("Modularity:", modularity_score, "\n")

    # Identify hub nodes (top 5% by degree for more selectivity)
    degree_threshold <- quantile(node_degrees, 0.95)
    hub_nodes <- names(node_degrees[node_degrees >= degree_threshold])
    cat("Hub nodes (top 5% by degree):", length(hub_nodes), "\n")

    # Create results list
    results <- list(
      network = network,
      node_degrees = node_degrees,
      betweenness = betweenness,
      closeness = closeness,
      communities = communities,
      community_count = community_count,
      modularity = modularity_score,
      hub_nodes = hub_nodes,
      edge_list = edge_list,
      group1_name = group1_name,
      group2_name = group2_name
    )

    return(results)

  }, error = function(e) {
    cat("Error in cross-network analysis:", e$message, "\n")
    return(NULL)
  })
}

# Perform cross-correlation analyses
cat("\n\n========== CROSS-CORRELATION ANALYSES ==========\n")

# Helper function to perform cross-correlation with multiple thresholds
perform_taxonomic_cross_correlation <- function(table1, table2, group1_name, group2_name) {
  cat("\n\n----- ", group1_name, " vs ", group2_name, " -----\n", sep="")

  # For cross-correlations, start with lower threshold (0.4) since cross-kingdom correlations are typically weaker
  tryCatch({
    cross_cor <- create_taxonomic_cross_correlation_matrix(table1, table2, threshold = 0.4)

    if (!is.null(cross_cor) && sum(cross_cor$cor_matrix != 0) > 0) {
      cat("\nFound correlations above threshold 0.4\n")
      cross_network <- analyze_taxonomic_cross_network(cross_cor, group1_name, group2_name)
      return(cross_network)
    } else {
      cat("\nNo correlations above threshold 0.4. Trying threshold 0.3...\n")

      # Try with threshold 0.3
      cross_cor_03 <- create_taxonomic_cross_correlation_matrix(table1, table2, threshold = 0.3)

      if (!is.null(cross_cor_03) && sum(cross_cor_03$cor_matrix != 0) > 0) {
        cat("\nFound correlations above threshold 0.3\n")
        cross_network <- analyze_taxonomic_cross_network(cross_cor_03,
                                                        paste(group1_name, "(0.3)"),
                                                        paste(group2_name, "(0.3)"))
        return(cross_network)
      } else {
        cat("\nNo correlations above threshold 0.3. Trying threshold 0.25...\n")

        # Try with threshold 0.25
        cross_cor_025 <- create_taxonomic_cross_correlation_matrix(table1, table2, threshold = 0.25)

        if (!is.null(cross_cor_025) && sum(cross_cor_025$cor_matrix != 0) > 0) {
          cat("\nFound correlations above threshold 0.25\n")
          cross_network <- analyze_taxonomic_cross_network(cross_cor_025,
                                                          paste(group1_name, "(0.25)"),
                                                          paste(group2_name, "(0.25)"))
          return(cross_network)
        } else {
          cat("\nNo significant cross-correlations found even at threshold 0.25\n")
          cat("This may indicate weak bacteria-fungi interactions in this environment\n")
          return(NULL)
        }
      }
    }
  }, error = function(e) {
    cat("Error in cross-correlation analysis:", e$message, "\n")
    return(NULL)
  })
}

# Perform the 4 cross-correlations for bacteria vs fungi in each environment/time combination

# 1. Bacteria Alpine Old vs Fungi Alpine Old
if (!is.null(bacteria_alpine_old) && !is.null(fungi_alpine_old)) {
  cat("Attempting cross-correlation: Bacteria Alpine Old vs Fungi Alpine Old\n")
  bacteria_fungi_alpine_old_network <- perform_taxonomic_cross_correlation(
    bacteria_alpine_old, fungi_alpine_old,
    "Bacteria_Alpine_Old", "Fungi_Alpine_Old"
  )
  if (!is.null(bacteria_fungi_alpine_old_network)) {
    cat("SUCCESS: Bacteria Alpine Old vs Fungi Alpine Old cross-network created\n")
  } else {
    cat("FAILED: No cross-network created for Bacteria Alpine Old vs Fungi Alpine Old\n")
  }
} else {
  cat("SKIPPED: Missing data for Bacteria Alpine Old vs Fungi Alpine Old\n")
}

# 2. Bacteria Alpine New vs Fungi Alpine New
if (!is.null(bacteria_alpine_new) && !is.null(fungi_alpine_new)) {
  cat("Attempting cross-correlation: Bacteria Alpine New vs Fungi Alpine New\n")
  bacteria_fungi_alpine_new_network <- perform_taxonomic_cross_correlation(
    bacteria_alpine_new, fungi_alpine_new,
    "Bacteria_Alpine_New", "Fungi_Alpine_New"
  )
  if (!is.null(bacteria_fungi_alpine_new_network)) {
    cat("SUCCESS: Bacteria Alpine New vs Fungi Alpine New cross-network created\n")
  } else {
    cat("FAILED: No cross-network created for Bacteria Alpine New vs Fungi Alpine New\n")
  }
} else {
  cat("SKIPPED: Missing data for Bacteria Alpine New vs Fungi Alpine New\n")
}

# 3. Bacteria Farming Old vs Fungi Farming Old
if (!is.null(bacteria_farming_old) && !is.null(fungi_farming_old)) {
  cat("Attempting cross-correlation: Bacteria Farming Old vs Fungi Farming Old\n")
  bacteria_fungi_farming_old_network <- perform_taxonomic_cross_correlation(
    bacteria_farming_old, fungi_farming_old,
    "Bacteria_Farming_Old", "Fungi_Farming_Old"
  )
  if (!is.null(bacteria_fungi_farming_old_network)) {
    cat("SUCCESS: Bacteria Farming Old vs Fungi Farming Old cross-network created\n")
  } else {
    cat("FAILED: No cross-network created for Bacteria Farming Old vs Fungi Farming Old\n")
  }
} else {
  cat("SKIPPED: Missing data for Bacteria Farming Old vs Fungi Farming Old\n")
}

# 4. Bacteria Farming New vs Fungi Farming New
if (!is.null(bacteria_farming_new) && !is.null(fungi_farming_new)) {
  cat("Attempting cross-correlation: Bacteria Farming New vs Fungi Farming New\n")
  bacteria_fungi_farming_new_network <- perform_taxonomic_cross_correlation(
    bacteria_farming_new, fungi_farming_new,
    "Bacteria_Farming_New", "Fungi_Farming_New"
  )
  if (!is.null(bacteria_fungi_farming_new_network)) {
    cat("SUCCESS: Bacteria Farming New vs Fungi Farming New cross-network created\n")
  } else {
    cat("FAILED: No cross-network created for Bacteria Farming New vs Fungi Farming New\n")
  }
} else {
  cat("SKIPPED: Missing data for Bacteria Farming New vs Fungi Farming New\n")
}

# Function to plot individual networks with improved visualization
plot_individual_network <- function(network_result, group_name, taxonomic_level) {
  if (is.null(network_result)) {
    cat("Cannot plot network: network result is NULL\n")
    return(NULL)
  }

  tryCatch({
    network <- network_result$network

    # Set up layout with better spacing
    layout <- layout_with_fr(network, niter = 1000)

    # Color nodes by community with solid, distinct colors (no transparency for better visibility)
    if (network_result$community_count <= 12) {
      # Use a predefined set of distinct, solid colors
      distinct_colors <- c("#E31A1C", "#1F78B4", "#33A02C", "#FF7F00", "#6A3D9A", "#B15928",
                          "#A6CEE3", "#B2DF8A", "#FB9A99", "#FDBF6F", "#CAB2D6", "#FFFF99")
      community_colors <- distinct_colors[1:network_result$community_count]
    } else {
      # For larger numbers, use a distinct color palette
      community_colors <- rainbow(network_result$community_count, s = 0.8, v = 0.9)
    }
    # Use solid colors (no transparency) for better edge visibility
    node_colors <- community_colors[membership(network_result$communities)]

    # Scale down node sizes significantly and make them more proportional
    max_degree <- max(network_result$node_degrees)
    min_degree <- min(network_result$node_degrees)
    # Scale nodes between 2 and 8 (much smaller than before)
    node_sizes <- 2 + (network_result$node_degrees - min_degree) / (max_degree - min_degree) * 6

    # Improve edge visualization - solid colors with better contrast
    base_edge_colors <- ifelse(E(network)$sign == "positive", "#2E8B57", "#DC143C")  # Sea green and crimson
    edge_colors <- base_edge_colors  # No transparency for better visibility

    # Scale edge widths better
    edge_widths <- 0.5 + abs(E(network)$correlation) * 2

    # More selective hub node identification (top 5% instead of 10%)
    hub_threshold <- quantile(network_result$node_degrees, 0.95)
    is_hub <- network_result$node_degrees >= hub_threshold

    # Create filename
    filename <- paste0(group_name, "_", taxonomic_level, "_network.pdf")

    # Create plot with higher resolution
    pdf(filename, width = 14, height = 12)

    # Plot network with improved parameters
    plot(network,
         layout = layout,
         vertex.color = node_colors,
         vertex.size = node_sizes,
         vertex.frame.color = "black",
         vertex.frame.width = 0.5,
         vertex.label = ifelse(is_hub, V(network)$name, ""),
         vertex.label.cex = 0.9,  # Larger font for better readability
         vertex.label.color = "black",
         vertex.label.font = 2,  # Bold font
         vertex.label.dist = 0.5,  # Distance from node center
         vertex.label.degree = -pi/2,  # Position labels above nodes
         edge.color = edge_colors,
         edge.width = edge_widths,
         edge.curved = 0.1,  # Slight curve for better visibility
         main = paste(group_name, "Network at", taxonomic_level, "Level"),
         sub = paste("Nodes:", vcount(network), "| Edges:", ecount(network),
                    "| Communities:", network_result$community_count,
                    "| Hub nodes (top 5%):", sum(is_hub)),
         cex.main = 1.2,
         cex.sub = 1.0)

    # Add improved legend with better positioning
    legend("bottomright",
           legend = c("Positive correlation", "Negative correlation", "Hub nodes (top 5%)", "Communities"),
           col = c("#2E8B57", "#DC143C", "black", "gray"),
           lty = c(1, 1, NA, NA),
           pch = c(NA, NA, 16, 15),
           cex = 0.9,
           bg = "white",
           box.lty = 1)

    dev.off()

    cat("Network plot saved:", filename, "\n")
    cat("Hub nodes identified:", sum(is_hub), "out of", vcount(network), "total nodes\n")

  }, error = function(e) {
    cat("Error creating network plot:", e$message, "\n")
  })
}

# Function to plot cross-correlation networks with improved visualization
plot_cross_network <- function(cross_network_result, group1_name, group2_name, taxonomic_level) {
  if (is.null(cross_network_result)) {
    cat("Cannot plot cross-network: network result is NULL\n")
    return(NULL)
  }

  tryCatch({
    network <- cross_network_result$network

    # Check if network has vertices
    if (vcount(network) == 0) {
      cat("Network has no vertices, cannot plot\n")
      return(NULL)
    }

    # Try different layouts for better visualization
    # First try bipartite layout, if it fails use force-directed
    layout <- tryCatch({
      layout_as_bipartite(network)
    }, error = function(e) {
      cat("Bipartite layout failed, using force-directed layout\n")
      layout_with_fr(network, niter = 1000)
    })

    # If layout is still problematic, use a simple layout
    if (is.null(layout) || any(is.na(layout))) {
      cat("Using circle layout as fallback\n")
      layout <- layout_in_circle(network)
    }

    # Color nodes by group with distinct, solid colors
    group1_color <- "#4169E1"  # Royal blue for group 1
    group2_color <- "#FF6347"  # Tomato red for group 2
    node_colors <- ifelse(V(network)$type == cross_network_result$group1_name, group1_color, group2_color)

    # Scale down node sizes significantly
    max_degree <- max(cross_network_result$node_degrees)
    min_degree <- min(cross_network_result$node_degrees)
    # Scale nodes between 3 and 10 (smaller than before)
    node_sizes <- 3 + (cross_network_result$node_degrees - min_degree) / (max_degree - min_degree) * 7

    # Improve edge visualization - solid colors
    base_edge_colors <- ifelse(E(network)$sign == "positive", "#228B22", "#B22222")  # Forest green and firebrick
    edge_colors <- base_edge_colors  # No transparency

    # Scale edge widths
    edge_widths <- 0.8 + abs(E(network)$correlation) * 2.5

    # More selective hub node identification (top 5%)
    hub_threshold <- quantile(cross_network_result$node_degrees, 0.95)
    is_hub <- cross_network_result$node_degrees >= hub_threshold

    # Create filename
    filename <- paste0(group1_name, "_vs_", group2_name, "_", taxonomic_level, "_cross_network.pdf")

    # Create plot with higher resolution
    pdf(filename, width = 16, height = 12)

    # Plot network with improved parameters
    plot(network,
         layout = layout,
         vertex.color = node_colors,
         vertex.size = node_sizes,
         vertex.frame.color = "black",
         vertex.frame.width = 0.8,
         vertex.label = ifelse(is_hub, V(network)$name, ""),
         vertex.label.cex = 1.0,  # Larger font
         vertex.label.color = "black",
         vertex.label.font = 2,  # Bold font
         vertex.label.dist = 0.8,  # Distance from node center
         vertex.label.degree = -pi/2,  # Position labels above nodes
         edge.color = edge_colors,
         edge.width = edge_widths,
         edge.curved = 0.15,  # More curve for cross-network
         main = paste(group1_name, "vs", group2_name, "Cross-Network at", taxonomic_level, "Level"),
         sub = paste("Nodes:", vcount(network), "| Edges:", ecount(network),
                    "| Communities:", cross_network_result$community_count,
                    "| Hub nodes (top 5%):", sum(is_hub)),
         cex.main = 1.3,
         cex.sub = 1.1)

    # Add improved legend
    legend("bottomright",
           legend = c(paste(group1_name, "nodes"), paste(group2_name, "nodes"),
                     "Positive correlation", "Negative correlation", "Hub nodes (top 5%)"),
           col = c(group1_color, group2_color, "#228B22", "#B22222", "black"),
           pch = c(16, 16, NA, NA, 16),
           lty = c(NA, NA, 1, 1, NA),
           cex = 0.9,
           bg = "white",
           box.lty = 1)

    dev.off()

    cat("Cross-network plot saved:", filename, "\n")
    cat("Hub nodes identified:", sum(is_hub), "out of", vcount(network), "total nodes\n")
    cat("Group 1 (", cross_network_result$group1_name, ") nodes:", sum(V(network)$type == cross_network_result$group1_name), "\n")
    cat("Group 2 (", cross_network_result$group2_name, ") nodes:", sum(V(network)$type == cross_network_result$group2_name), "\n")

  }, error = function(e) {
    cat("Error creating cross-network plot:", e$message, "\n")
    cat("Error details:", e$message, "\n")
  })
}

# Function to save hub node analysis
save_hub_node_analysis <- function(network_result, group_name, taxonomic_level) {
  if (is.null(network_result)) {
    return(NULL)
  }

  tryCatch({
    # Create hub node data frame
    hub_data <- data.frame(
      Taxon = network_result$hub_nodes,
      Degree = network_result$node_degrees[network_result$hub_nodes],
      Betweenness = network_result$betweenness[network_result$hub_nodes],
      Closeness = network_result$closeness[network_result$hub_nodes],
      Community = membership(network_result$communities)[network_result$hub_nodes],
      stringsAsFactors = FALSE
    )

    # Sort by degree
    hub_data <- hub_data[order(hub_data$Degree, decreasing = TRUE), ]

    # Save to CSV
    filename <- paste0(group_name, "_", taxonomic_level, "_hub_nodes.csv")
    write.csv(hub_data, filename, row.names = FALSE)

    cat("Hub node analysis saved:", filename, "\n")

    return(hub_data)

  }, error = function(e) {
    cat("Error saving hub node analysis:", e$message, "\n")
    return(NULL)
  })
}

# Function to save community analysis
save_community_analysis <- function(network_result, group_name, taxonomic_level) {
  if (is.null(network_result)) {
    return(NULL)
  }

  tryCatch({
    # Create community data frame
    community_data <- data.frame(
      Taxon = network_result$taxa_names,
      Community = membership(network_result$communities),
      Degree = network_result$node_degrees,
      Betweenness = network_result$betweenness,
      Closeness = network_result$closeness,
      stringsAsFactors = FALSE
    )

    # Sort by community and degree
    community_data <- community_data[order(community_data$Community, community_data$Degree, decreasing = c(FALSE, TRUE)), ]

    # Save to CSV
    filename <- paste0(group_name, "_", taxonomic_level, "_communities.csv")
    write.csv(community_data, filename, row.names = FALSE)

    cat("Community analysis saved:", filename, "\n")

    return(community_data)

  }, error = function(e) {
    cat("Error saving community analysis:", e$message, "\n")
    return(NULL)
  })
}

# Function to create network comparison table
create_network_comparison_table <- function(network_results_list, taxonomic_level) {
  tryCatch({
    comparison_data <- data.frame(
      Network = character(),
      Nodes = numeric(),
      Edges = numeric(),
      Avg_Degree = numeric(),
      Max_Degree = numeric(),
      Communities = numeric(),
      Modularity = numeric(),
      Hub_Nodes = numeric(),
      Density = numeric(),
      stringsAsFactors = FALSE
    )

    for (name in names(network_results_list)) {
      result <- network_results_list[[name]]
      if (!is.null(result)) {
        comparison_data <- rbind(comparison_data, data.frame(
          Network = name,
          Nodes = vcount(result$network),
          Edges = ecount(result$network),
          Avg_Degree = mean(result$node_degrees),
          Max_Degree = max(result$node_degrees),
          Communities = result$community_count,
          Modularity = result$modularity,
          Hub_Nodes = length(result$hub_nodes),
          Density = edge_density(result$network),
          stringsAsFactors = FALSE
        ))
      }
    }

    # Save comparison table
    filename <- paste0("Network_Comparison_", taxonomic_level, "_Level.csv")
    write.csv(comparison_data, filename, row.names = FALSE)

    cat("Network comparison table saved:", filename, "\n")

    return(comparison_data)

  }, error = function(e) {
    cat("Error creating comparison table:", e$message, "\n")
    return(NULL)
  })
}

# Function to create statistical comparison plots
create_statistical_plots <- function(individual_networks, cross_networks, taxonomic_level) {
  tryCatch({
    # Prepare data for individual networks
    if (length(individual_networks) > 0) {
      individual_stats <- data.frame(
        Network = names(individual_networks),
        Nodes = sapply(individual_networks, function(x) vcount(x$network)),
        Edges = sapply(individual_networks, function(x) ecount(x$network)),
        Avg_Degree = sapply(individual_networks, function(x) mean(x$node_degrees)),
        Max_Degree = sapply(individual_networks, function(x) max(x$node_degrees)),
        Communities = sapply(individual_networks, function(x) x$community_count),
        Modularity = sapply(individual_networks, function(x) x$modularity),
        Density = sapply(individual_networks, function(x) edge_density(x$network)),
        stringsAsFactors = FALSE
      )

      # Add grouping variables
      individual_stats$Kingdom <- ifelse(grepl("Bacteria", individual_stats$Network), "Bacteria", "Fungi")
      individual_stats$Environment <- ifelse(grepl("Alpine", individual_stats$Network), "Alpine", "Farming")
      individual_stats$Time <- ifelse(grepl("Old", individual_stats$Network), "Old", "New")

      # Create statistical comparison plots with better formatting
      pdf(paste0("Network_Statistics_", taxonomic_level, "_Level.pdf"), width = 18, height = 14)

      # Set up multi-panel plot with better margins
      par(mfrow = c(3, 3), mar = c(5, 4, 3, 2), oma = c(2, 2, 2, 2))

      # Plot 1: Number of nodes by kingdom and environment
      boxplot(Nodes ~ Kingdom + Environment, data = individual_stats,
              main = "Number of Nodes", xlab = "Kingdom + Environment", ylab = "Nodes",
              col = adjustcolor(c("#87CEEB", "#98D982", "#87CEEB", "#98D982"), alpha.f = 0.8), las = 2)

      # Plot 2: Number of edges by kingdom and environment
      boxplot(Edges ~ Kingdom + Environment, data = individual_stats,
              main = "Number of Edges", xlab = "Kingdom + Environment", ylab = "Edges",
              col = adjustcolor(c("#87CEEB", "#98D982", "#87CEEB", "#98D982"), alpha.f = 0.8), las = 2)

      # Plot 3: Average degree by kingdom and environment
      boxplot(Avg_Degree ~ Kingdom + Environment, data = individual_stats,
              main = "Average Degree", xlab = "Kingdom + Environment", ylab = "Average Degree",
              col = adjustcolor(c("#87CEEB", "#98D982", "#87CEEB", "#98D982"), alpha.f = 0.8), las = 2)

      # Plot 4: Communities by kingdom and environment
      boxplot(Communities ~ Kingdom + Environment, data = individual_stats,
              main = "Number of Communities", xlab = "Kingdom + Environment", ylab = "Communities",
              col = adjustcolor(c("#87CEEB", "#98D982", "#87CEEB", "#98D982"), alpha.f = 0.8), las = 2)

      # Plot 5: Modularity by kingdom and environment
      boxplot(Modularity ~ Kingdom + Environment, data = individual_stats,
              main = "Modularity", xlab = "Kingdom + Environment", ylab = "Modularity",
              col = adjustcolor(c("#87CEEB", "#98D982", "#87CEEB", "#98D982"), alpha.f = 0.8), las = 2)

      # Plot 6: Network density by kingdom and environment
      boxplot(Density ~ Kingdom + Environment, data = individual_stats,
              main = "Network Density", xlab = "Kingdom + Environment", ylab = "Density",
              col = adjustcolor(c("#87CEEB", "#98D982", "#87CEEB", "#98D982"), alpha.f = 0.8), las = 2)

      # Plot 7: Nodes comparison Old vs New
      boxplot(Nodes ~ Time, data = individual_stats,
              main = "Nodes: Old vs New", xlab = "Time", ylab = "Nodes",
              col = adjustcolor(c("#FFB6C1", "#98FB98"), alpha.f = 0.8))

      # Plot 8: Modularity comparison Old vs New
      boxplot(Modularity ~ Time, data = individual_stats,
              main = "Modularity: Old vs New", xlab = "Time", ylab = "Modularity",
              col = adjustcolor(c("#FFB6C1", "#98FB98"), alpha.f = 0.8))

      # Plot 9: Summary barplot
      barplot(individual_stats$Nodes, names.arg = individual_stats$Network,
              main = "Network Sizes Overview", xlab = "Networks", ylab = "Number of Nodes",
              col = adjustcolor(rainbow(nrow(individual_stats)), alpha.f = 0.7),
              las = 2, cex.names = 0.7)

      dev.off()

      cat("Statistical comparison plots saved: Network_Statistics_", taxonomic_level, "_Level.pdf\n")
    }

    # Create cross-network statistics if available
    if (length(cross_networks) > 0) {
      cross_stats <- data.frame(
        Network = names(cross_networks),
        Nodes = sapply(cross_networks, function(x) vcount(x$network)),
        Edges = sapply(cross_networks, function(x) ecount(x$network)),
        Avg_Degree = sapply(cross_networks, function(x) mean(x$node_degrees)),
        Communities = sapply(cross_networks, function(x) x$community_count),
        Modularity = sapply(cross_networks, function(x) x$modularity),
        stringsAsFactors = FALSE
      )

      # Create cross-network comparison plot
      pdf(paste0("Cross_Network_Statistics_", taxonomic_level, "_Level.pdf"), width = 12, height = 8)

      par(mfrow = c(2, 3), mar = c(4, 4, 3, 2))

      barplot(cross_stats$Nodes, names.arg = cross_stats$Network,
              main = "Cross-Network Nodes", ylab = "Nodes",
              col = adjustcolor("#DDA0DD", alpha.f = 0.8), las = 2, cex.names = 0.8)

      barplot(cross_stats$Edges, names.arg = cross_stats$Network,
              main = "Cross-Network Edges", ylab = "Edges",
              col = adjustcolor("#DDA0DD", alpha.f = 0.8), las = 2, cex.names = 0.8)

      barplot(cross_stats$Avg_Degree, names.arg = cross_stats$Network,
              main = "Cross-Network Average Degree", ylab = "Average Degree",
              col = adjustcolor("#DDA0DD", alpha.f = 0.8), las = 2, cex.names = 0.8)

      barplot(cross_stats$Communities, names.arg = cross_stats$Network,
              main = "Cross-Network Communities", ylab = "Communities",
              col = adjustcolor("#DDA0DD", alpha.f = 0.8), las = 2, cex.names = 0.8)

      barplot(cross_stats$Modularity, names.arg = cross_stats$Network,
              main = "Cross-Network Modularity", ylab = "Modularity",
              col = adjustcolor("#DDA0DD", alpha.f = 0.8), las = 2, cex.names = 0.8)

      # Summary plot
      plot(cross_stats$Edges, cross_stats$Modularity,
           pch = 16, col = adjustcolor("#DDA0DD", alpha.f = 0.8), cex = 2,
           main = "Cross-Network: Edges vs Modularity",
           xlab = "Number of Edges", ylab = "Modularity")
      text(cross_stats$Edges, cross_stats$Modularity,
           labels = cross_stats$Network, pos = 3, cex = 0.7)

      dev.off()

      cat("Cross-network statistics saved: Cross_Network_Statistics_", taxonomic_level, "_Level.pdf\n")
    }

  }, error = function(e) {
    cat("Error creating statistical plots:", e$message, "\n")
  })
}

# Generate all plots and outputs
cat("\n\n========== GENERATING PLOTS AND OUTPUTS ==========\n")

# Plot individual networks and save analyses
individual_networks <- list()

if (exists("bacteria_alpine_old_network") && !is.null(bacteria_alpine_old_network)) {
  plot_individual_network(bacteria_alpine_old_network, "Bacteria_Alpine_Old", TAXONOMIC_LEVEL)
  save_hub_node_analysis(bacteria_alpine_old_network, "Bacteria_Alpine_Old", TAXONOMIC_LEVEL)
  save_community_analysis(bacteria_alpine_old_network, "Bacteria_Alpine_Old", TAXONOMIC_LEVEL)
  individual_networks[["Bacteria_Alpine_Old"]] <- bacteria_alpine_old_network
}

if (exists("bacteria_alpine_new_network") && !is.null(bacteria_alpine_new_network)) {
  plot_individual_network(bacteria_alpine_new_network, "Bacteria_Alpine_New", TAXONOMIC_LEVEL)
  save_hub_node_analysis(bacteria_alpine_new_network, "Bacteria_Alpine_New", TAXONOMIC_LEVEL)
  save_community_analysis(bacteria_alpine_new_network, "Bacteria_Alpine_New", TAXONOMIC_LEVEL)
  individual_networks[["Bacteria_Alpine_New"]] <- bacteria_alpine_new_network
}

if (exists("bacteria_farming_old_network") && !is.null(bacteria_farming_old_network)) {
  plot_individual_network(bacteria_farming_old_network, "Bacteria_Farming_Old", TAXONOMIC_LEVEL)
  save_hub_node_analysis(bacteria_farming_old_network, "Bacteria_Farming_Old", TAXONOMIC_LEVEL)
  save_community_analysis(bacteria_farming_old_network, "Bacteria_Farming_Old", TAXONOMIC_LEVEL)
  individual_networks[["Bacteria_Farming_Old"]] <- bacteria_farming_old_network
}

if (exists("bacteria_farming_new_network") && !is.null(bacteria_farming_new_network)) {
  plot_individual_network(bacteria_farming_new_network, "Bacteria_Farming_New", TAXONOMIC_LEVEL)
  save_hub_node_analysis(bacteria_farming_new_network, "Bacteria_Farming_New", TAXONOMIC_LEVEL)
  save_community_analysis(bacteria_farming_new_network, "Bacteria_Farming_New", TAXONOMIC_LEVEL)
  individual_networks[["Bacteria_Farming_New"]] <- bacteria_farming_new_network
}

if (exists("fungi_alpine_old_network") && !is.null(fungi_alpine_old_network)) {
  plot_individual_network(fungi_alpine_old_network, "Fungi_Alpine_Old", TAXONOMIC_LEVEL)
  save_hub_node_analysis(fungi_alpine_old_network, "Fungi_Alpine_Old", TAXONOMIC_LEVEL)
  save_community_analysis(fungi_alpine_old_network, "Fungi_Alpine_Old", TAXONOMIC_LEVEL)
  individual_networks[["Fungi_Alpine_Old"]] <- fungi_alpine_old_network
}

if (exists("fungi_alpine_new_network") && !is.null(fungi_alpine_new_network)) {
  plot_individual_network(fungi_alpine_new_network, "Fungi_Alpine_New", TAXONOMIC_LEVEL)
  save_hub_node_analysis(fungi_alpine_new_network, "Fungi_Alpine_New", TAXONOMIC_LEVEL)
  save_community_analysis(fungi_alpine_new_network, "Fungi_Alpine_New", TAXONOMIC_LEVEL)
  individual_networks[["Fungi_Alpine_New"]] <- fungi_alpine_new_network
}

if (exists("fungi_farming_old_network") && !is.null(fungi_farming_old_network)) {
  plot_individual_network(fungi_farming_old_network, "Fungi_Farming_Old", TAXONOMIC_LEVEL)
  save_hub_node_analysis(fungi_farming_old_network, "Fungi_Farming_Old", TAXONOMIC_LEVEL)
  save_community_analysis(fungi_farming_old_network, "Fungi_Farming_Old", TAXONOMIC_LEVEL)
  individual_networks[["Fungi_Farming_Old"]] <- fungi_farming_old_network
}

if (exists("fungi_farming_new_network") && !is.null(fungi_farming_new_network)) {
  plot_individual_network(fungi_farming_new_network, "Fungi_Farming_New", TAXONOMIC_LEVEL)
  save_hub_node_analysis(fungi_farming_new_network, "Fungi_Farming_New", TAXONOMIC_LEVEL)
  save_community_analysis(fungi_farming_new_network, "Fungi_Farming_New", TAXONOMIC_LEVEL)
  individual_networks[["Fungi_Farming_New"]] <- fungi_farming_new_network
}

# Plot cross-correlation networks
cross_networks <- list()

if (exists("bacteria_fungi_alpine_old_network") && !is.null(bacteria_fungi_alpine_old_network)) {
  plot_cross_network(bacteria_fungi_alpine_old_network, "Bacteria_Alpine_Old", "Fungi_Alpine_Old", TAXONOMIC_LEVEL)
  save_hub_node_analysis(bacteria_fungi_alpine_old_network, "Bacteria_Fungi_Alpine_Old", TAXONOMIC_LEVEL)
  cross_networks[["Bacteria_Fungi_Alpine_Old"]] <- bacteria_fungi_alpine_old_network
}

if (exists("bacteria_fungi_alpine_new_network") && !is.null(bacteria_fungi_alpine_new_network)) {
  plot_cross_network(bacteria_fungi_alpine_new_network, "Bacteria_Alpine_New", "Fungi_Alpine_New", TAXONOMIC_LEVEL)
  save_hub_node_analysis(bacteria_fungi_alpine_new_network, "Bacteria_Fungi_Alpine_New", TAXONOMIC_LEVEL)
  cross_networks[["Bacteria_Fungi_Alpine_New"]] <- bacteria_fungi_alpine_new_network
}

if (exists("bacteria_fungi_farming_old_network") && !is.null(bacteria_fungi_farming_old_network)) {
  plot_cross_network(bacteria_fungi_farming_old_network, "Bacteria_Farming_Old", "Fungi_Farming_Old", TAXONOMIC_LEVEL)
  save_hub_node_analysis(bacteria_fungi_farming_old_network, "Bacteria_Fungi_Farming_Old", TAXONOMIC_LEVEL)
  cross_networks[["Bacteria_Fungi_Farming_Old"]] <- bacteria_fungi_farming_old_network
}

if (exists("bacteria_fungi_farming_new_network") && !is.null(bacteria_fungi_farming_new_network)) {
  plot_cross_network(bacteria_fungi_farming_new_network, "Bacteria_Farming_New", "Fungi_Farming_New", TAXONOMIC_LEVEL)
  save_hub_node_analysis(bacteria_fungi_farming_new_network, "Bacteria_Fungi_Farming_New", TAXONOMIC_LEVEL)
  cross_networks[["Bacteria_Fungi_Farming_New"]] <- bacteria_fungi_farming_new_network
}

# Create comparison tables
if (length(individual_networks) > 0) {
  individual_comparison <- create_network_comparison_table(individual_networks, paste0(TAXONOMIC_LEVEL, "_Individual"))
}

if (length(cross_networks) > 0) {
  cross_comparison <- create_network_comparison_table(cross_networks, paste0(TAXONOMIC_LEVEL, "_Cross"))
}

# Create statistical plots
cat("\n========== CREATING STATISTICAL PLOTS ==========\n")
create_statistical_plots(individual_networks, cross_networks, TAXONOMIC_LEVEL)

cat("\n\n========== ANALYSIS COMPLETED ==========\n")
cat("Taxonomic level:", TAXONOMIC_LEVEL, "\n")
cat("Individual networks analyzed:", length(individual_networks), "\n")
cat("Cross-correlation networks analyzed:", length(cross_networks), "\n")
cat("All plots, hub node analyses, community analyses, and comparison tables generated!\n")

# Print summary of generated files
cat("\n========== GENERATED FILES SUMMARY ==========\n")
cat("PDF Network Plots:\n")
for (name in names(individual_networks)) {
  cat("  -", paste0(name, "_", TAXONOMIC_LEVEL, "_network.pdf"), "\n")
}
for (name in names(cross_networks)) {
  # Extract group names for cross-network filename
  if (name == "Bacteria_Fungi_Alpine_Old") {
    cat("  - Bacteria_Alpine_Old_vs_Fungi_Alpine_Old_", TAXONOMIC_LEVEL, "_cross_network.pdf\n")
  } else if (name == "Bacteria_Fungi_Alpine_New") {
    cat("  - Bacteria_Alpine_New_vs_Fungi_Alpine_New_", TAXONOMIC_LEVEL, "_cross_network.pdf\n")
  } else if (name == "Bacteria_Fungi_Farming_Old") {
    cat("  - Bacteria_Farming_Old_vs_Fungi_Farming_Old_", TAXONOMIC_LEVEL, "_cross_network.pdf\n")
  } else if (name == "Bacteria_Fungi_Farming_New") {
    cat("  - Bacteria_Farming_New_vs_Fungi_Farming_New_", TAXONOMIC_LEVEL, "_cross_network.pdf\n")
  }
}

cat("\nCSV Analysis Files:\n")
for (name in names(individual_networks)) {
  cat("  -", paste0(name, "_", TAXONOMIC_LEVEL, "_hub_nodes.csv"), "\n")
  cat("  -", paste0(name, "_", TAXONOMIC_LEVEL, "_communities.csv"), "\n")
}
for (name in names(cross_networks)) {
  cat("  -", paste0(name, "_", TAXONOMIC_LEVEL, "_hub_nodes.csv"), "\n")
}

cat("\nComparison Tables:\n")
if (length(individual_networks) > 0) {
  cat("  - Network_Comparison_", TAXONOMIC_LEVEL, "_Individual_Level.csv\n")
}
if (length(cross_networks) > 0) {
  cat("  - Network_Comparison_", TAXONOMIC_LEVEL, "_Cross_Level.csv\n")
}

cat("\n🎉 Complete taxonomic network analysis finished successfully! 🎉\n")
